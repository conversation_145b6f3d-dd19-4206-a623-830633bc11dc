import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { Button } from "@/components/ui/button";
import { Select } from "@/components/ui/select";
import { useUser } from "@/contexts/user-context";

// Define data types
interface AnalyticsData {
  hiringMetrics: {
    openPositions: number;
    activeInterviews: number;
    offersSent: number;
    offerAcceptRate: number;
    avgTimeToHire: number;
    totalHires: number;
  };
  recruitersPerformance: {
    id: string;
    name: string;
    activeRequirements: number;
    candidatesSourced: number;
    interviewsScheduled: number;
    offersExtended: number;
    hires: number;
    conversionRate: number;
  }[];
  hiringTrends: {
    month: string;
    applications: number;
    interviews: number;
    offers: number;
    hires: number;
  }[];
  departmentHires: {
    department: string;
    plannedHires: number;
    actualHires: number;
    openPositions: number;
  }[];
}

export function ManagerAnalytics() {
  const [isLoading, setIsLoading] = useState(true);
  const [dateRange, setDateRange] = useState("last90days");
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null);

  // Simulate API call to get analytics data
  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1200));
      
      // Mock analytics data
      setAnalyticsData({
        hiringMetrics: {
          openPositions: 24,
          activeInterviews: 38,
          offersSent: 12,
          offerAcceptRate: 0.75,
          avgTimeToHire: 28,
          totalHires: 18
        },
        recruitersPerformance: [
          {
            id: "r1",
            name: "Alex Johnson",
            activeRequirements: 8,
            candidatesSourced: 42,
            interviewsScheduled: 31,
            offersExtended: 15,
            hires: 11,
            conversionRate: 0.26
          },
          {
            id: "r2",
            name: "Jamie Smith",
            activeRequirements: 12,
            candidatesSourced: 58,
            interviewsScheduled: 39,
            offersExtended: 12,
            hires: 8,
            conversionRate: 0.14
          },
          {
            id: "r3",
            name: "Rohan Patel",
            activeRequirements: 5,
            candidatesSourced: 27,
            interviewsScheduled: 18,
            offersExtended: 7,
            hires: 5,
            conversionRate: 0.19
          },
          {
            id: "r4",
            name: "Maria Garcia",
            activeRequirements: 10,
            candidatesSourced: 49,
            interviewsScheduled: 30,
            offersExtended: 14,
            hires: 9,
            conversionRate: 0.18
          }
        ],
        hiringTrends: [
          { month: "December", applications: 105, interviews: 52, offers: 15, hires: 11 },
          { month: "January", applications: 128, interviews: 67, offers: 18, hires: 14 },
          { month: "February", applications: 143, interviews: 72, offers: 22, hires: 17 },
          { month: "March", applications: 160, interviews: 89, offers: 26, hires: 20 },
          { month: "April", applications: 172, interviews: 94, offers: 29, hires: 22 },
        ],
        departmentHires: [
          { department: "Engineering", plannedHires: 15, actualHires: 12, openPositions: 3 },
          { department: "Product", plannedHires: 8, actualHires: 5, openPositions: 3 },
          { department: "Design", plannedHires: 6, actualHires: 4, openPositions: 2 },
          { department: "Marketing", plannedHires: 5, actualHires: 5, openPositions: 0 },
          { department: "Sales", plannedHires: 12, actualHires: 9, openPositions: 3 },
          { department: "Customer Support", plannedHires: 10, actualHires: 7, openPositions: 3 }
        ]
      });
      
      setIsLoading(false);
    };
    
    fetchData();
  }, [dateRange]);

  const renderBarChart = (data: AnalyticsData["hiringTrends"]) => {
    const maxValue = Math.max(...data.flatMap(d => [d.applications, d.interviews, d.offers, d.hires]));
    const normalize = (value: number) => (value / maxValue) * 100;
    
    return (
      <div className="flex flex-col">
        <div className="flex items-end h-64 space-x-8 mt-4">
          {data.map((month, index) => (
            <div key={month.month} className="flex-1 flex flex-col items-center">
              <div className="w-full flex justify-around space-x-1 h-52">
                <motion.div 
                  className="bg-blue-400 rounded-t w-3"
                  initial={{ height: 0 }}
                  animate={{ height: `${normalize(month.applications)}%` }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                />
                <motion.div 
                  className="bg-purple-400 rounded-t w-3"
                  initial={{ height: 0 }}
                  animate={{ height: `${normalize(month.interviews)}%` }}
                  transition={{ duration: 0.5, delay: index * 0.1 + 0.1 }}
                />
                <motion.div 
                  className="bg-amber-400 rounded-t w-3"
                  initial={{ height: 0 }}
                  animate={{ height: `${normalize(month.offers)}%` }}
                  transition={{ duration: 0.5, delay: index * 0.1 + 0.2 }}
                />
                <motion.div 
                  className="bg-green-400 rounded-t w-3"
                  initial={{ height: 0 }}
                  animate={{ height: `${normalize(month.hires)}%` }}
                  transition={{ duration: 0.5, delay: index * 0.1 + 0.3 }}
                />
              </div>
              <div className="text-xs font-medium text-gray-600 mt-2">{month.month}</div>
            </div>
          ))}
        </div>
        <div className="flex justify-center mt-4 space-x-6">
          <div className="flex items-center">
            <div className="h-3 w-3 bg-blue-400 rounded mr-1"></div>
            <span className="text-xs text-gray-600">Applications</span>
          </div>
          <div className="flex items-center">
            <div className="h-3 w-3 bg-purple-400 rounded mr-1"></div>
            <span className="text-xs text-gray-600">Interviews</span>
          </div>
          <div className="flex items-center">
            <div className="h-3 w-3 bg-amber-400 rounded mr-1"></div>
            <span className="text-xs text-gray-600">Offers</span>
          </div>
          <div className="flex items-center">
            <div className="h-3 w-3 bg-green-400 rounded mr-1"></div>
            <span className="text-xs text-gray-600">Hires</span>
          </div>
        </div>
      </div>
    );
  };

  const renderProgressBars = (data: AnalyticsData["departmentHires"]) => {
    return (
      <div className="space-y-4 mt-4">
        {data.map(dept => {
          const percentage = dept.plannedHires > 0 
            ? Math.round((dept.actualHires / dept.plannedHires) * 100) 
            : 0;
            
          return (
            <div key={dept.department} className="mb-4">
              <div className="flex justify-between items-center mb-1">
                <span className="text-sm font-medium text-gray-700">{dept.department}</span>
                <span className="text-sm text-gray-600">
                  {dept.actualHires} / {dept.plannedHires} ({percentage}%)
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2.5">
                <motion.div 
                  className="h-2.5 rounded-full bg-blue-600"
                  style={{ width: `${percentage}%` }}
                  initial={{ width: 0 }}
                  animate={{ width: `${percentage}%` }}
                  transition={{ duration: 0.8, ease: "easeOut" }}
                />
              </div>
              <div className="text-xs text-gray-500 mt-1">
                {dept.openPositions > 0 
                  ? `${dept.openPositions} open position${dept.openPositions > 1 ? 's' : ''} remaining` 
                  : 'All positions filled'}
              </div>
            </div>
          );
        })}
      </div>
    );
  };

  if (isLoading) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-center">
          <svg
            className="animate-spin h-10 w-10 text-blue-600 mx-auto mb-4"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle
              className="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              strokeWidth="4"
            ></circle>
            <path
              className="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            ></path>
          </svg>
          <p className="text-gray-600">Loading analytics data...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col space-y-6 p-1 overflow-auto">
      <div className="bg-white rounded-lg shadow border border-gray-200 p-6">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Recruiting Analytics</h1>
            <p className="text-gray-600">Detailed hiring metrics and team performance overview</p>
          </div>
          
          <div className="flex items-center space-x-2">
            <label htmlFor="dateRange" className="text-sm text-gray-600">
              Time Period:
            </label>
            <Select value={dateRange} onValueChange={setDateRange} >
              <option value="last30days">Last 30 Days</option>
              <option value="last90days">Last 90 Days</option>
              <option value="last6months">Last 6 Months</option>
              <option value="lastyear">Last Year</option>
            </Select>
            <Button
              variant="outline"
              onClick={() => {
                setDateRange(prev => prev);
                setIsLoading(true);
              }}
              className="ml-2"
            >
              Refresh
            </Button>
          </div>
        </div>
        
        {analyticsData && (
          <div className="space-y-8">
            {/* Top metrics */}
            <div>
              <h2 className="text-lg font-semibold text-gray-800 mb-4">Key Metrics</h2>
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: 0.1 }}
                  className="bg-blue-50 rounded-lg p-4 border border-blue-100"
                >
                  <div className="text-sm text-blue-600 font-medium">Open Positions</div>
                  <div className="text-2xl font-bold text-gray-900 mt-1">
                    {analyticsData.hiringMetrics.openPositions}
                  </div>
                </motion.div>
                
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: 0.2 }}
                  className="bg-purple-50 rounded-lg p-4 border border-purple-100"
                >
                  <div className="text-sm text-purple-600 font-medium">Active Interviews</div>
                  <div className="text-2xl font-bold text-gray-900 mt-1">
                    {analyticsData.hiringMetrics.activeInterviews}
                  </div>
                </motion.div>
                
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: 0.3 }}
                  className="bg-amber-50 rounded-lg p-4 border border-amber-100"
                >
                  <div className="text-sm text-amber-600 font-medium">Offers Sent</div>
                  <div className="text-2xl font-bold text-gray-900 mt-1">
                    {analyticsData.hiringMetrics.offersSent}
                  </div>
                </motion.div>
                
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: 0.4 }}
                  className="bg-green-50 rounded-lg p-4 border border-green-100"
                >
                  <div className="text-sm text-green-600 font-medium">Offer Accept Rate</div>
                  <div className="text-2xl font-bold text-gray-900 mt-1">
                    {(analyticsData.hiringMetrics.offerAcceptRate * 100).toFixed(1)}%
                  </div>
                </motion.div>
                
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: 0.5 }}
                  className="bg-indigo-50 rounded-lg p-4 border border-indigo-100"
                >
                  <div className="text-sm text-indigo-600 font-medium">Avg Days to Hire</div>
                  <div className="text-2xl font-bold text-gray-900 mt-1">
                    {analyticsData.hiringMetrics.avgTimeToHire}
                  </div>
                </motion.div>
                
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: 0.6 }}
                  className="bg-rose-50 rounded-lg p-4 border border-rose-100"
                >
                  <div className="text-sm text-rose-600 font-medium">Total Hires</div>
                  <div className="text-2xl font-bold text-gray-900 mt-1">
                    {analyticsData.hiringMetrics.totalHires}
                  </div>
                </motion.div>
              </div>
            </div>
            
            {/* Hiring trends chart */}
            <div>
              <h2 className="text-lg font-semibold text-gray-800 mb-3">Hiring Pipeline Trends</h2>
              {renderBarChart(analyticsData.hiringTrends)}
            </div>
            
            {/* Department hiring progress */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              <div>
                <h2 className="text-lg font-semibold text-gray-800 mb-3">Department Hiring Progress</h2>
                {renderProgressBars(analyticsData.departmentHires)}
              </div>
              
              {/* Recruiter performance */}
              <div>
                <h2 className="text-lg font-semibold text-gray-800 mb-3">Recruiter Performance</h2>
                <div className="overflow-x-auto border rounded-lg">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Recruiter
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Requirements
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Sourced
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Interviews
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Offers
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Hires
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Conv. Rate
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {analyticsData.recruitersPerformance.map((recruiter, index) => (
                        <motion.tr 
                          key={recruiter.id}
                          initial={{ opacity: 0, y: 10 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ duration: 0.3, delay: index * 0.1 }}
                          className="hover:bg-gray-50"
                        >
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm font-medium text-gray-900">{recruiter.name}</div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {recruiter.activeRequirements}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {recruiter.candidatesSourced}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {recruiter.interviewsScheduled}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {recruiter.offersExtended}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {recruiter.hires}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`px-2 py-1 text-xs rounded-full ${
                              recruiter.conversionRate >= 0.2 
                                ? 'bg-green-100 text-green-800' 
                                : recruiter.conversionRate >= 0.15 
                                ? 'bg-amber-100 text-amber-800' 
                                : 'bg-red-100 text-red-800'
                            }`}>
                              {(recruiter.conversionRate * 100).toFixed(1)}%
                            </span>
                          </td>
                        </motion.tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
