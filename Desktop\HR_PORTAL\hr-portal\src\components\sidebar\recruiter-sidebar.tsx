import { Link, useLocation } from "react-router-dom";
import {
  Calendar,
  Home,
  ClipboardList,
  UserPlus,
  FileText,
  BarChart2,
  UserCheck,
  Lock,
  LogOut,
  MessageSquare,
  Users,
} from "lucide-react";

import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar";

interface MenuItem {
  title: string;
  url: string;
  icon: React.ComponentType<{ className?: string }>;
}

interface MenuSection {
  title?: string;
  items: MenuItem[];
}

const menuSections: MenuSection[] = [
  {
    items: [
      { title: "Dashboard", url: "/recruiter/dashboard", icon: Home },
    ]
  },
  {
    title: "RECRUITMENT",
    items: [
      { title: "Assigned Requirements", url: "/recruiter/requirements", icon: ClipboardList },
    ]
  },
  {
    title: "CANDIDATES",
    items: [
      { title: "Register Candidate", url: "/register-candidate", icon: UserPlus },
      { title: "Peer Assigned Profiles", url: "/recruiter/peer-assigned-profiles", icon: FileText },
    ]
  },
  {
    title: "GENERAL",
    items: [
      { title: "Analytics", url: "/recruiter/analytics", icon: BarChart2 },
      { title: "Profile Analysis", url: "/recruiter/profile-analysis", icon: UserCheck },
      { title: "Candidate Messages", url: "/recruiter/messages", icon: MessageSquare },
      { title: "Calendar", url: "/calendar", icon: Calendar },
      { title: "Help and Support", url: "/help-and-support", icon: Users },
      { title: "Change Password", url: "/change-password", icon: Lock },
      { title: "Logout", url: "/logout", icon: LogOut },
    ]
  }
];

export function RecruiterSidebar() {
  const location = useLocation();
  const currentPath = location.pathname;

  return (
    <Sidebar className="bg-white border-r border-gray-200 w-48">
      <SidebarHeader className="p-3 border-b border-gray-200">
        <h2 className="text-lg font-semibold text-gray-900">Recruiter</h2>
      </SidebarHeader>

      <SidebarContent className="px-2 py-2 flex-1 overflow-y-auto">
        {menuSections.map((section, sectionIndex) => (
          <SidebarGroup key={sectionIndex} className="mb-1">
            {section.title && (
              <SidebarGroupLabel className="px-2 py-1 text-xs font-semibold text-gray-500 uppercase tracking-wider">
                {section.title}
              </SidebarGroupLabel>
            )}
            <SidebarGroupContent>
              <SidebarMenu className="space-y-0">
                {section.items.map((item) => {
                  const isActive = currentPath === item.url;

                  return (
                    <SidebarMenuItem key={item.title}>
                      <SidebarMenuButton
                        asChild
                        isActive={isActive}
                        className={`w-full rounded-md px-2 py-1.5 text-sm font-medium transition-colors duration-150 ${
                          isActive
                            ? "bg-gray-100 text-gray-900"
                            : "text-gray-700 hover:bg-gray-100 hover:text-gray-900"
                        }`}
                      >
                        <Link to={item.url} className="flex items-center">
                          <item.icon
                            className={`h-4 w-4 mr-2 ${
                              isActive
                                ? "text-gray-900"
                                : "text-gray-500"
                            }`}
                          />
                          <span>{item.title}</span>
                        </Link>
                      </SidebarMenuButton>
                    </SidebarMenuItem>
                  );
                })}
              </SidebarMenu>
            </SidebarGroupContent>
          </SidebarGroup>
        ))}
      </SidebarContent>
    </Sidebar>
  );
}
