import { useState } from "react";
import { Search, FileText, Users, Plus, Info } from "lucide-react";
import { AnimatedTableWrapper } from "@/components/ui/animated-table-wrapper";
import { AnimatedTableRow } from "@/components/ui/animated-table-row";
import { AnimatedSortIcon } from "@/components/ui/animated-sort-icon";
import { Pagination } from "@/components/ui/pagination";
import { useTableAnimation } from "@/hooks/use-table-animation";

// Define the Requirement type
interface Requirement {
  id: number;
  jobId: string;
  jobPosted: string;
  jobStatus: "Open" | "Closed" | "On Hold" | "Urgent";
  client: string;
  role: string;
  positions: number;
}

// Sample data for the table
const requirementsData: Requirement[] = Array.from({ length: 30 }, (_, i) => ({
  id: i + 1,
  jobId: `JOB-${2023 + Math.floor(Math.random() * 100)}`,
  jobPosted: new Date(
    2023 + Math.floor(Math.random() * 2),
    Math.floor(Math.random() * 12),
    Math.floor(Math.random() * 28) + 1
  )
    .toISOString()
    .split("T")[0],
  jobStatus: ["Open", "Closed", "On Hold", "Urgent"][
    Math.floor(Math.random() * 4)
  ] as "Open" | "Closed" | "On Hold" | "Urgent",
  client: [
    "Acme Corp",
    "TechGiant",
    "Innovate Inc",
    "Global Systems",
    "NextGen",
  ][Math.floor(Math.random() * 5)],
  role: [
    "Frontend Developer",
    "Backend Engineer",
    "Full Stack",
    "DevOps",
    "UI/UX Designer",
  ][Math.floor(Math.random() * 5)],
  positions: Math.floor(Math.random() * 5) + 1,
}));

// Define column configuration
interface Column {
  key: keyof Requirement;
  label: string;
  sortable: boolean;
}

export function AssignedRequirements() {
  return (
    <div className="h-full flex flex-col">
      <div className="bg-white rounded-lg shadow border border-gray-200 p-4 flex-1">
        <div className="flex-1 flex flex-col">
          <RequirementsTable />
        </div>
      </div>
    </div>
  );
}

function RequirementsTable() {
  const columns: Column[] = [
    { key: "jobPosted", label: "Job Posted", sortable: true },
    { key: "jobId", label: "Job ID", sortable: true },
    { key: "jobStatus", label: "Job Status", sortable: true },
    { key: "client", label: "Client", sortable: true },
    { key: "role", label: "Role", sortable: true },
    { key: "positions", label: "No of Positions", sortable: true },
  ];

  // Animation controls
  const { isLoading, animateSorting, animatePagination } = useTableAnimation();

  // State for pagination
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  // State for search and filters
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("");

  // State for sorting
  const [sortConfig, setSortConfig] = useState<{
    key: keyof Requirement | null;
    direction: "ascending" | "descending" | null;
  }>({
    key: null,
    direction: null,
  });

  // Filter requirements based on search term and status
  const filteredRequirements = requirementsData.filter((requirement) => {
    // Check if requirement matches search term
    const matchesSearch =
      searchTerm === "" ||
      Object.values(requirement).some((val) =>
        String(val).toLowerCase().includes(searchTerm.toLowerCase())
      );

    // Check if requirement matches status filter
    const matchesStatus =
      statusFilter === "" || requirement.jobStatus === statusFilter;

    // Return true only if both conditions are met
    return matchesSearch && matchesStatus;
  });

  // Sort requirements if sort config is set
  const sortedRequirements = [...filteredRequirements].sort((a, b) => {
    if (!sortConfig.key) return 0;

    const aValue = a[sortConfig.key];
    const bValue = b[sortConfig.key];

    if (aValue < bValue) {
      return sortConfig.direction === "ascending" ? -1 : 1;
    }
    if (aValue > bValue) {
      return sortConfig.direction === "ascending" ? 1 : -1;
    }
    return 0;
  });

  // Get current requirements for pagination
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentRequirements = sortedRequirements.slice(
    indexOfFirstItem,
    indexOfLastItem
  );

  // Change page with animation
  const paginate = async (pageNumber: number) => {
    await animatePagination();
    setCurrentPage(pageNumber);
  };

  // Handle sort with animation
  const handleSort = async (key: keyof Requirement) => {
    // Apply a small shake animation to the table when sorting
    await animateSorting();

    let direction: "ascending" | "descending" | null = "ascending";

    if (sortConfig.key === key && sortConfig.direction === "ascending") {
      direction = "descending";
    } else if (
      sortConfig.key === key &&
      sortConfig.direction === "descending"
    ) {
      direction = null;
    }

    setSortConfig({ key, direction });
  };

  // Get status badge color
  const getStatusColor = (status: Requirement["jobStatus"]) => {
    switch (status) {
      case "Open":
        return "bg-green-100 text-green-800";
      case "Closed":
        return "bg-gray-100 text-gray-800";
      case "On Hold":
        return "bg-yellow-100 text-yellow-800";
      case "Urgent":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <div className="flex flex-col">
      <div className="mb-4 flex flex-col sm:flex-row justify-between gap-4">
        <div className="relative flex-1">
          <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
            <Search className="h-4 w-4 text-gray-400" />
          </div>
          <input
            type="text"
            className="bg-white border border-gray-300 text-gray-900 text-sm rounded-md block w-full pl-10 p-2.5 focus:ring-blue-500 focus:border-blue-500"
            placeholder="Search requirements..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <div className="flex items-center gap-3">
          {/* Status Filter */}
          <select
            className="bg-white border border-gray-300 text-gray-900 text-sm rounded-md p-2.5 focus:ring-blue-500 focus:border-blue-500"
            onChange={(e) => setStatusFilter(e.target.value)}
            value={statusFilter}
          >
            <option value="">All Status</option>
            <option value="Open">Open</option>
            <option value="Closed">Closed</option>
            <option value="On Hold">On Hold</option>
            <option value="Urgent">Urgent</option>
          </select>

          {/* Page Size Selector */}
          <select
            className="bg-white border border-gray-300 text-gray-900 text-sm rounded-md p-2.5 focus:ring-blue-500 focus:border-blue-500"
            onChange={(e) => setItemsPerPage(Number(e.target.value))}
            value={itemsPerPage}
          >
            <option value="10">10 per page</option>
            <option value="20">20 per page</option>
            <option value="50">50 per page</option>
            <option value="100">100 per page</option>
          </select>
        </div>
      </div>

      {/* Table with fixed header and scrollable body */}
      <AnimatedTableWrapper
        isLoading={isLoading}
        className="border border-gray-200 rounded-md overflow-hidden flex-1"
      >
        <div className="overflow-x-auto overflow-y-auto h-full max-h-[calc(100vh-250px)]">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                {columns.map((column) => (
                  <th
                    key={String(column.key)}
                    scope="col"
                    className="sticky top-0 z-10 px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer bg-gray-50 border-b border-gray-200"
                    onClick={() => column.sortable && handleSort(column.key)}
                  >
                    <div className="flex items-center gap-1">
                      {column.label}
                      <AnimatedSortIcon
                        direction={
                          sortConfig.key === column.key
                            ? sortConfig.direction
                            : null
                        }
                        active={sortConfig.key === column.key}
                        size={14}
                      />
                    </div>
                  </th>
                ))}
                {/* Action Column Headers */}
                <th className="sticky top-0 z-10 px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50 border-b border-gray-200">
                  View JD
                </th>
                <th className="sticky top-0 z-10 px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50 border-b border-gray-200">
                  Resume Match
                </th>
                <th className="sticky top-0 z-10 px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50 border-b border-gray-200">
                  Add Candidate
                </th>
                <th className="sticky top-0 z-10 px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50 border-b border-gray-200">
                  Details
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {currentRequirements.length > 0 ? (
                currentRequirements.map((requirement, index) => (
                  <AnimatedTableRow key={requirement.id} index={index}>
                    {columns.map((column) => (
                      <td
                        key={`${requirement.id}-${String(column.key)}`}
                        className="px-6 py-4 whitespace-nowrap text-sm text-gray-500"
                      >
                        {column.key === "jobStatus" ? (
                          <span
                            className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(
                              requirement.jobStatus
                            )}`}
                          >
                            {requirement.jobStatus}
                          </span>
                        ) : (
                          String(requirement[column.key])
                        )}
                      </td>
                    ))}

                    {/* Action Buttons */}
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">
                      <button
                        className="text-blue-600 hover:text-blue-800"
                        title="View Job Description"
                      >
                        <FileText className="h-4 w-4 mx-auto" />
                      </button>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">
                      <button
                        className="text-blue-600 hover:text-blue-800"
                        title="Resume Match"
                      >
                        <Users className="h-4 w-4 mx-auto" />
                      </button>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">
                      <button
                        className="text-blue-600 hover:text-blue-800"
                        title="Add Candidate"
                      >
                        <Plus className="h-4 w-4 mx-auto" />
                      </button>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">
                      <button
                        className="text-blue-600 hover:text-blue-800"
                        title="View Details"
                      >
                        <Info className="h-4 w-4 mx-auto" />
                      </button>
                    </td>
                  </AnimatedTableRow>
                ))
              ) : (
                <tr>
                  <td
                    colSpan={
                      columns.length + 4
                    } /* Add 4 for the action columns */
                    className="px-6 py-4 text-center text-sm text-gray-500"
                  >
                    No requirements found
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </AnimatedTableWrapper>

      {/* Pagination */}
      <div className="flex items-center justify-between mt-4 text-sm text-gray-700">
        <div>
          Showing {indexOfFirstItem + 1} to{" "}
          {Math.min(indexOfLastItem, filteredRequirements.length)} of{" "}
          {filteredRequirements.length} requirements
        </div>
        <Pagination
          currentPage={currentPage}
          totalPages={Math.ceil(filteredRequirements.length / itemsPerPage)}
          onPageChange={paginate}
          className="flex items-center gap-1"
        />
      </div>
    </div>
  );
}
