import React, { useState } from "react";
import { AnimatedTableWrapper } from "@/components/ui/animated-table-wrapper";
import { AnimatedTableRow } from "@/components/ui/animated-table-row";
import { AnimatedSortIcon } from "@/components/ui/animated-sort-icon";
import { AnimatedPagination } from "@/components/ui/animated-pagination";
import { useTableAnimation } from "@/hooks/use-table-animation";

// Define the PeerAssignedProfile type
interface PeerAssignedProfile {
  priority: string;
  date: string;
  viewJD: string;
  name: string;
  client: string;
  profile: string;
  skills: string;
  reviewStatus: string;
  resume: string;
  details: string;
}

// Sample data
const mockData: PeerAssignedProfile[] = Array.from({ length: 30 }, (_, i) => ({
  priority: ["High", "Medium", "Low"][i % 3],
  date: `2024-06-${(i % 30) + 1}`,
  viewJD: "View",
  name: `Candidate ${i + 1}`,
  client: ["Acme Corp", "TechGiant", "Innovate Inc"][i % 3],
  profile: ["Frontend Developer", "Backend Engineer", "QA Analyst"][i % 3],
  skills: ["React, Node.js", "Java, Spring", "Python, Django"][i % 3],
  reviewStatus: ["Pending", "Reviewed", "Rejected"][i % 3],
  resume: "Download",
  details: "View Details",
}));

// Define columns
const columns = [
  { key: "priority", label: "Priority", sortable: true },
  { key: "date", label: "Date", sortable: true },
  { key: "viewJD", label: "View JD", sortable: false },
  { key: "name", label: "Name", sortable: true },
  { key: "client", label: "Client", sortable: true },
  { key: "profile", label: "Profile", sortable: true },
  { key: "skills", label: "Skills", sortable: true },
  { key: "reviewStatus", label: "Review Status", sortable: true },
  { key: "resume", label: "Resume", sortable: false },
  { key: "details", label: "Details", sortable: false },
];

export default function PeerAssignedProfiles() {
  // State for pagination
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  // State for search
  const [searchTerm, setSearchTerm] = useState("");
  // State for sorting
  const [sortConfig, setSortConfig] = useState<{ key: string | null; direction: "ascending" | "descending" | null }>({ key: null, direction: null });
  // Animation controls
  const { isLoading, animateSorting, animatePagination } = useTableAnimation();

  // Filter data based on search term
  const filteredData = mockData.filter((row) => {
    return (
      searchTerm === "" ||
      Object.values(row).some((val) =>
        String(val).toLowerCase().includes(searchTerm.toLowerCase())
      )
    );
  });

  // Sort data if sort config is set
  const sortedData = [...filteredData].sort((a, b) => {
    if (!sortConfig.key) return 0;
    const aValue = a[sortConfig.key as keyof PeerAssignedProfile];
    const bValue = b[sortConfig.key as keyof PeerAssignedProfile];
    if (aValue < bValue) {
      return sortConfig.direction === "ascending" ? -1 : 1;
    }
    if (aValue > bValue) {
      return sortConfig.direction === "ascending" ? 1 : -1;
    }
    return 0;
  });

  // Get current page data
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentRows = sortedData.slice(indexOfFirstItem, indexOfLastItem);

  // Change page with animation
  const paginate = async (pageNumber: number) => {
    await animatePagination();
    setCurrentPage(pageNumber);
  };

  // Handle sort with animation
  const handleSort = async (key: string) => {
    await animateSorting();
    let direction: "ascending" | "descending" | null = "ascending";
    if (sortConfig.key === key && sortConfig.direction === "ascending") {
      direction = "descending";
    } else if (sortConfig.key === key && sortConfig.direction === "descending") {
      direction = null;
    }
    setSortConfig({ key, direction });
  };

  return (
    <div className="flex flex-col">
      <div className="mb-4 flex flex-col sm:flex-row justify-between gap-4">
        <div className="relative flex-1">
          <input
            type="text"
            className="bg-white border border-gray-300 text-gray-900 text-sm rounded-md block w-full pl-3 p-2.5 focus:ring-blue-500 focus:border-blue-500"
            placeholder="Search profiles..."
            value={searchTerm}
            onChange={async (e) => {
              const newSearchTerm = e.target.value;
              if (Math.abs(newSearchTerm.length - searchTerm.length) > 2) {
                await animateSorting();
              }
              setSearchTerm(newSearchTerm);
              setCurrentPage(1);
            }}
          />
        </div>
        <div className="flex items-center gap-3">
          {/* Page Size Selector */}
          <select
            className="bg-white border border-gray-300 text-gray-900 text-sm rounded-md p-2.5 focus:ring-blue-500 focus:border-blue-500"
            onChange={async (e) => {
              await animatePagination();
              setItemsPerPage(Number(e.target.value));
              setCurrentPage(1);
            }}
            value={itemsPerPage}
          >
            <option value="10">10 per page</option>
            <option value="20">20 per page</option>
            <option value="50">50 per page</option>
            <option value="100">100 per page</option>
          </select>
        </div>
      </div>
      <AnimatedTableWrapper isLoading={isLoading} className="border border-gray-200 rounded-md overflow-hidden flex-1">
        <div className="overflow-x-auto overflow-y-auto h-full max-h-[calc(100vh-250px)]">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                {columns.map((column) => (
                  <th
                    key={column.key}
                    scope="col"
                    className="sticky top-0 z-10 px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer bg-gray-50 border-b border-gray-200"
                    onClick={() => column.sortable && handleSort(column.key)}
                  >
                    <div className="flex items-center gap-1">
                      {column.label}
                      <AnimatedSortIcon
                        direction={
                          sortConfig.key === column.key
                            ? sortConfig.direction === "ascending"
                              ? "ascending"
                              : "descending"
                            : null
                        }
                        active={sortConfig.key === column.key}
                        size={14}
                      />
                    </div>
                  </th>
                ))}
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {currentRows.length > 0 ? (
                currentRows.map((row, index) => (
                  <AnimatedTableRow key={index} index={index}>
                    {columns.map((column) => (
                      <td
                        key={`${index}-${column.key}`}
                        className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center"
                      >
                        {row[column.key as keyof PeerAssignedProfile]}
                      </td>
                    ))}
                  </AnimatedTableRow>
                ))
              ) : (
                <tr>
                  <td
                    colSpan={columns.length}
                    className="px-6 py-4 text-center text-sm text-gray-500"
                  >
                    No profiles found
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </AnimatedTableWrapper>
      {/* Pagination */}
      <div className="flex items-center justify-between mt-4 text-sm text-gray-700">
        <div>
          Showing {indexOfFirstItem + 1} to {Math.min(indexOfLastItem, filteredData.length)} of {filteredData.length} profiles
        </div>
        <AnimatedPagination
          currentPage={currentPage}
          totalPages={Math.ceil(filteredData.length / itemsPerPage)}
          onPageChange={paginate}
        />
      </div>
    </div>
  );
} 