import React, { useState, useEffect } from "react";
import { AnimatedTableWrapper } from "@/components/ui/animated-table-wrapper";
import { AnimatedTableRow } from "@/components/ui/animated-table-row";
import { AnimatedSortIcon } from "@/components/ui/animated-sort-icon";
import { AnimatedPagination } from "@/components/ui/animated-pagination";
import { useTableAnimation } from "@/hooks/use-table-animation";
import { ApiService, type PeerAssignedCandidate } from "@/services/api";
import { useUser } from "@/contexts/user-context";
import { Button } from "@/components/ui/button";
import { Eye, Download, FileText } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

// Define the local PeerAssignedProfile type for table display
interface PeerAssignedProfile {
  priority: string;
  date: string;
  viewJD: string;
  name: string;
  client: string;
  profile: string;
  skills: string;
  recruiter: string;
  reviewStatus: string;
  resume: string;
  details: string;
  id: number;
  job_id: number;
}

// Function to convert API candidate to local profile format
const convertApiCandidateToProfile = (candidate: PeerAssignedCandidate): PeerAssignedProfile => {
  // Determine priority based on status or other criteria
  const getPriority = (status: string): string => {
    if (status.includes('Reject')) return 'Low';
    if (status.includes('Pending')) return 'High';
    return 'Medium';
  };

  // Format date
  const formatDate = (dateString: string): string => {
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('en-GB'); // DD/MM/YYYY format
    } catch {
      return dateString;
    }
  };

  return {
    priority: getPriority(candidate.status),
    date: formatDate(candidate.date_created),
    viewJD: 'View JD',
    name: candidate.name,
    client: candidate.client,
    profile: candidate.profile,
    skills: candidate.skills,
    recruiter: candidate.recruiter,
    reviewStatus: candidate.status,
    resume: candidate.resume_present ? 'Download' : 'Not Available',
    details: 'View Details',
    id: candidate.id,
    job_id: candidate.job_id,
  };
};

// Define columns with widths
const columns = [
  { key: "priority", label: "Priority", sortable: true, width: "w-20" },
  { key: "date", label: "Date", sortable: true, width: "w-24" },
  { key: "viewJD", label: "View JD", sortable: false, width: "w-16" },
  { key: "name", label: "Name", sortable: true, width: "w-32" },
  { key: "client", label: "Client", sortable: true, width: "w-28" },
  { key: "profile", label: "Profile", sortable: true, width: "w-36" },
  { key: "skills", label: "Skills", sortable: true, width: "w-48" },
  { key: "recruiter", label: "Recruiter", sortable: true, width: "w-28" },
  { key: "reviewStatus", label: "Review Status", sortable: true, width: "w-32" },
  { key: "resume", label: "Resume", sortable: false, width: "w-16" },
  { key: "details", label: "Details", sortable: false, width: "w-16" },
];

export default function PeerAssignedProfiles() {
  const { userName } = useUser();

  // State for API data
  const [profiles, setProfiles] = useState<PeerAssignedProfile[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // State for candidate details modal
  const [selectedCandidate, setSelectedCandidate] = useState<PeerAssignedCandidate | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [candidatesData, setCandidatesData] = useState<PeerAssignedCandidate[]>([]);

  // State for pagination
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  // State for search
  const [searchTerm, setSearchTerm] = useState("");
  // State for sorting
  const [sortConfig, setSortConfig] = useState<{ key: string | null; direction: "ascending" | "descending" | null }>({ key: null, direction: null });
  // Animation controls
  const { isLoading: tableLoading, animateSorting, animatePagination } = useTableAnimation();

  // Fetch peer assigned profiles from API
  useEffect(() => {
    const fetchProfiles = async () => {
      if (!userName) return;

      try {
        setLoading(true);
        setError(null);
        const response = await ApiService.fetchPeerAssignedProfiles(userName);
        setCandidatesData(response.candidates); // Store raw data for modal
        const convertedProfiles = response.candidates.map(convertApiCandidateToProfile);
        setProfiles(convertedProfiles);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch peer assigned profiles');
        console.error('Failed to fetch peer assigned profiles:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchProfiles();
  }, [userName]);

  // Handle opening candidate details modal
  const handleViewDetails = (candidateId: number) => {
    const candidate = candidatesData.find(c => c.id === candidateId);
    if (candidate) {
      setSelectedCandidate(candidate);
      setIsModalOpen(true);
    }
  };

  // Handle closing modal
  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedCandidate(null);
  };

  // Format date for display
  const formatDate = (dateString: string | null): string => {
    if (!dateString) return 'Not specified';
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('en-GB');
    } catch {
      return dateString;
    }
  };

  // Render cell content with special handling for action columns
  const renderCellContent = (row: PeerAssignedProfile, columnKey: string) => {
    switch (columnKey) {
      case 'priority':
        return (
          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
            row.priority === 'High' ? 'bg-red-100 text-red-800' :
            row.priority === 'Medium' ? 'bg-yellow-100 text-yellow-800' :
            'bg-green-100 text-green-800'
          }`}>
            {row.priority}
          </span>
        );
      case 'viewJD':
        return (
          <Button variant="ghost" size="sm" className="p-2">
            <Eye className="h-4 w-4" />
          </Button>
        );
      case 'skills':
        return (
          <div className="max-w-xs truncate" title={row.skills}>
            {row.skills}
          </div>
        );
      case 'reviewStatus':
        return (
          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
            row.reviewStatus.includes('Pending') ? 'bg-yellow-100 text-yellow-800' :
            row.reviewStatus.includes('Reject') ? 'bg-red-100 text-red-800' :
            'bg-green-100 text-green-800'
          }`}>
            {row.reviewStatus}
          </span>
        );
      case 'resume':
        return row.resume === 'Download' ? (
          <Button variant="ghost" size="sm" className="p-2">
            <Download className="h-4 w-4" />
          </Button>
        ) : (
          <span className="text-gray-400">-</span>
        );
      case 'details':
        return (
          <Button
            variant="ghost"
            size="sm"
            className="p-2"
            onClick={() => handleViewDetails(row.id)}
          >
            <FileText className="h-4 w-4" />
          </Button>
        );
      default:
        return row[columnKey as keyof PeerAssignedProfile];
    }
  };

  // Filter data based on search term
  const filteredData = profiles.filter((row) => {
    return (
      searchTerm === "" ||
      Object.values(row).some((val) =>
        String(val).toLowerCase().includes(searchTerm.toLowerCase())
      )
    );
  });

  // Sort data if sort config is set
  const sortedData = [...filteredData].sort((a, b) => {
    if (!sortConfig.key) return 0;
    const aValue = a[sortConfig.key as keyof PeerAssignedProfile];
    const bValue = b[sortConfig.key as keyof PeerAssignedProfile];
    if (aValue < bValue) {
      return sortConfig.direction === "ascending" ? -1 : 1;
    }
    if (aValue > bValue) {
      return sortConfig.direction === "ascending" ? 1 : -1;
    }
    return 0;
  });

  // Get current page data
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentRows = sortedData.slice(indexOfFirstItem, indexOfLastItem);

  // Change page with animation
  const paginate = async (pageNumber: number) => {
    await animatePagination();
    setCurrentPage(pageNumber);
  };

  // Handle sort with animation
  const handleSort = async (key: string) => {
    await animateSorting();
    let direction: "ascending" | "descending" | null = "ascending";
    if (sortConfig.key === key && sortConfig.direction === "ascending") {
      direction = "descending";
    } else if (sortConfig.key === key && sortConfig.direction === "descending") {
      direction = null;
    }
    setSortConfig({ key, direction });
  };

  return (
    <div className="flex flex-col mt-1">
      <div className="mb-4 flex flex-col sm:flex-row justify-between gap-2">
        <div className="relative flex-1">
          <input
            type="text"
            className="bg-white border border-gray-300 text-gray-900 text-sm rounded-md block w-full pl-3 p-2.5 focus:ring-blue-500 focus:border-blue-500"
            placeholder="Search profiles..."
            value={searchTerm}
            onChange={async (e) => {
              const newSearchTerm = e.target.value;
              if (Math.abs(newSearchTerm.length - searchTerm.length) > 2) {
                await animateSorting();
              }
              setSearchTerm(newSearchTerm);
              setCurrentPage(1);
            }}
          />
        </div>
        <div className="flex items-center gap-3">
          {/* Page Size Selector */}
          <select
            className="bg-white border border-gray-300 text-gray-900 text-sm rounded-md p-2.5 focus:ring-blue-500 focus:border-blue-500"
            onChange={async (e) => {
              await animatePagination();
              setItemsPerPage(Number(e.target.value));
              setCurrentPage(1);
            }}
            value={itemsPerPage}
          >
            <option value="10">10 per page</option>
            <option value="20">20 per page</option>
            <option value="50">50 per page</option>
            <option value="100">100 per page</option>
          </select>
        </div>
      </div>
      <AnimatedTableWrapper isLoading={loading || tableLoading} className="border border-gray-200 rounded-md overflow-hidden flex-1">
        <div className="overflow-auto h-full max-h-[calc(100vh-210px)]" style={{ scrollbarGutter: 'stable' }}>
          <table className="w-full table-fixed divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                {columns.map((column) => (
                  <th
                    key={column.key}
                    scope="col"
                    className={`sticky top-0 z-10 px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer bg-gray-50 border-b border-gray-200 ${column.width}`}
                    onClick={() => column.sortable && handleSort(column.key)}
                  >
                    <div className="flex items-center gap-1">
                      {column.label}
                      <AnimatedSortIcon
                        direction={
                          sortConfig.key === column.key
                            ? sortConfig.direction === "ascending"
                              ? "ascending"
                              : "descending"
                            : null
                        }
                        active={sortConfig.key === column.key}
                        size={14}
                      />
                    </div>
                  </th>
                ))}
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {currentRows.length > 0 ? (
                currentRows.map((row, index) => (
                  <AnimatedTableRow key={`${row.id}-${index}`} index={index}>
                    {columns.map((column) => (
                      <td
                        key={`${row.id}-${column.key}`}
                        className="px-4 py-4 text-sm text-gray-500"
                      >
                        {renderCellContent(row, column.key)}
                      </td>
                    ))}
                  </AnimatedTableRow>
                ))
              ) : (
                <tr>
                  <td
                    colSpan={columns.length}
                    className="px-4 py-4 text-center text-sm text-gray-500"
                  >
                    {loading ? 'Loading profiles...' : error ? `Error: ${error}` : 'No profiles found'}
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </AnimatedTableWrapper>
      {/* Pagination */}
      <div className="flex items-center justify-between mt-4 text-sm text-gray-700">
        <div>
          Showing {indexOfFirstItem + 1} to {Math.min(indexOfLastItem, filteredData.length)} of {filteredData.length} profiles
        </div>
        <AnimatedPagination
          currentPage={currentPage}
          totalPages={Math.ceil(filteredData.length / itemsPerPage)}
          onPageChange={paginate}
        />
      </div>

      {/* Candidate Details Modal */}
      <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
        <DialogContent className="max-w-2xl max-h-[87vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Candidate Details</DialogTitle>
          </DialogHeader>

          {selectedCandidate && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="font-medium text-gray-700">ID:</span>
                    <span className="text-gray-900">{selectedCandidate.id}</span>
                  </div>

                  <div className="flex justify-between">
                    <span className="font-medium text-gray-700">Job Id:</span>
                    <span className="text-gray-900">{selectedCandidate.job_id}</span>
                  </div>

                  <div className="flex justify-between">
                    <span className="font-medium text-gray-700">Name:</span>
                    <span className="text-gray-900">{selectedCandidate.name}</span>
                  </div>

                  <div className="flex justify-between">
                    <span className="font-medium text-gray-700">Mobile:</span>
                    <span className="text-gray-900">{selectedCandidate.mobile}</span>
                  </div>

                  <div className="flex justify-between">
                    <span className="font-medium text-gray-700">Email:</span>
                    <span className="text-blue-600">{selectedCandidate.email}</span>
                  </div>

                  <div className="flex justify-between">
                    <span className="font-medium text-gray-700">Client:</span>
                    <span className="text-blue-600">{selectedCandidate.client}</span>
                  </div>

                  <div className="flex justify-between">
                    <span className="font-medium text-gray-700">Current Company:</span>
                    <span className="text-gray-900">{selectedCandidate.current_company}</span>
                  </div>

                  <div className="flex justify-between">
                    <span className="font-medium text-gray-700">Position:</span>
                    <span className="text-gray-900">{selectedCandidate.position}</span>
                  </div>

                  <div className="flex justify-between">
                    <span className="font-medium text-gray-700">Profile:</span>
                    <span className="text-blue-600">{selectedCandidate.profile}</span>
                  </div>

                  <div className="flex justify-between">
                    <span className="font-medium text-gray-700">Current Job Location:</span>
                    <span className="text-gray-900">{selectedCandidate.current_job_location}</span>
                  </div>

                  <div className="flex justify-between">
                    <span className="font-medium text-gray-700">Preferred Job Location:</span>
                    <span className="text-gray-900">{selectedCandidate.preferred_job_location || 'Not specified'}</span>
                  </div>

                  <div className="flex justify-between">
                    <span className="font-medium text-gray-700">Qualifications:</span>
                    <span className="text-blue-600">{selectedCandidate.qualifications}</span>
                  </div>

                  <div className="flex justify-between">
                    <span className="font-medium text-gray-700">Total Experience:</span>
                    <span className="text-gray-900">{selectedCandidate.experience}</span>
                  </div>

                  <div className="flex justify-between">
                    <span className="font-medium text-gray-700">Relevant Experience:</span>
                    <span className="text-gray-900">{selectedCandidate.relevant_experience}</span>
                  </div>
                </div>

                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="font-medium text-gray-700">Current ctc:</span>
                    <span className="text-gray-900">{selectedCandidate.current_ctc}</span>
                  </div>

                  <div className="flex justify-between">
                    <span className="font-medium text-gray-700">Expected ctc:</span>
                    <span className="text-gray-900">{selectedCandidate.expected_ctc}</span>
                  </div>

                  <div className="flex justify-between">
                    <span className="font-medium text-gray-700">Notice period:</span>
                    <span className="text-gray-900">{selectedCandidate.notice_period}</span>
                  </div>

                  <div className="flex justify-between">
                    <span className="font-medium text-gray-700">Linkedin:</span>
                    <span className="text-gray-900">{selectedCandidate.linkedin || 'Not specified'}</span>
                  </div>

                  <div className="flex justify-between">
                    <span className="font-medium text-gray-700">Holding Offer:</span>
                    <span className="text-gray-900">{selectedCandidate.holding_offer}</span>
                  </div>

                  <div className="flex justify-between">
                    <span className="font-medium text-gray-700">Recruiter:</span>
                    <span className="text-blue-600">{selectedCandidate.recruiter}</span>
                  </div>

                  <div className="flex justify-between">
                    <span className="font-medium text-gray-700">Management:</span>
                    <span className="text-gray-900">{selectedCandidate.management || 'Not specified'}</span>
                  </div>

                  <div className="flex justify-between">
                    <span className="font-medium text-gray-700">Status:</span>
                    <span className="text-blue-600">{selectedCandidate.status}</span>
                  </div>

                  <div className="flex justify-between">
                    <span className="font-medium text-gray-700">Remarks:</span>
                    <span className="text-gray-900">{selectedCandidate.remarks || 'Not specified'}</span>
                  </div>

                  <div className="flex justify-between">
                    <span className="font-medium text-gray-700">Period of notice:</span>
                    <span className="text-gray-900">{selectedCandidate.period_of_notice || 'Not specified'}</span>
                  </div>

                  <div className="flex justify-between">
                    <span className="font-medium text-gray-700">Last working Date:</span>
                    <span className="text-gray-900">{formatDate(selectedCandidate.last_working_date)}</span>
                  </div>

                  <div className="flex justify-between">
                    <span className="font-medium text-gray-700">Date created:</span>
                    <span className="text-gray-900">{formatDate(selectedCandidate.date_created)}</span>
                  </div>

                  <div className="flex justify-between">
                    <span className="font-medium text-gray-700">Time created:</span>
                    <span className="text-gray-900">{formatDate(selectedCandidate.date_created)}</span>
                  </div>
                </div>
              </div>

              {/* Skills section - full width */}
              <div className="border-t pt-1">
                <div className="flex justify-between">
                  <span className="font-medium text-gray-700">Skills:</span>
                  <span className="text-blue-600 text-right flex-1 ml-4">{selectedCandidate.skills || 'Not specified'}</span>
                </div>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}