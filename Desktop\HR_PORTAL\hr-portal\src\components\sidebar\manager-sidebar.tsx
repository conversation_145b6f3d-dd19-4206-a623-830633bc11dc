import React from "react";
import { Link, useLocation } from "react-router-dom";
import {
  Calendar,
  Home,
  ClipboardList,
  BarChart2,
  UserCheck,
  Lock,
  LogOut,
  Briefcase,
  UserPlus,
  FileSearch,
  UserCog,
  Users,
  ArrowRightLeft,
} from "lucide-react";
import {
  Sidebar,
  SidebarHeader,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuButton,
} from "@/components/ui/sidebar";

interface MenuItem {
  title: string;
  url: string;
  icon: React.ComponentType<{ className?: string }>;
}

interface MenuSection {
  title?: string;
  items: MenuItem[];
}

const menuSections: MenuSection[] = [
  {
    items: [
      { title: "Dashboard", url: "/manager/dashboard", icon: Home },
    ]
  },
  {
    title: "RECRUITMENT",
    items: [
      { title: "Job Listing", url: "/manager/job-listing", icon: Briefcase },
      { title: "Job Assignments", url: "/manager/job-assignments", icon: ClipboardList },
    ]
  },
  {
    title: "CANDIDATES",
    items: [
      { title: "Register Candidate", url: "/register-candidate", icon: UserPlus },
      { title: "Peer Assigned Profiles", url: "/manager/peer-assigned-profiles", icon: UserCheck },
      { title: "Profile Transfer", url: "/manager/profile-transfer", icon: ArrowRightLeft },
    ]
  },
  {
    title: "GENERAL",
    items: [
      { title: "Analytics", url: "/manager/analytics", icon: BarChart2 },
      { title: "User Accounts", url: "/manager/user-accounts", icon: UserCog },
      { title: "Calendar", url: "/calendar", icon: Calendar },
      { title: "Help and Support", url: "/help-and-support", icon: Users },
      { title: "Change Password", url: "/change-password", icon: Lock },
      { title: "Logout", url: "/logout", icon: LogOut },
    ]
  }
];

export function ManagerSidebar() {
  const location = useLocation();
  const currentPath = location.pathname;

  return (
    <Sidebar className="bg-white border-r border-gray-200 w-51">
      <SidebarHeader className="p-3 border-b border-gray-200">
        <h2 className="text-lg font-semibold text-gray-900">Manager</h2>
      </SidebarHeader>

      <SidebarContent className="px-2 py-2 flex-1 overflow-y-auto">
        {menuSections.map((section, sectionIndex) => (
          <SidebarGroup key={sectionIndex} className="mb-0.5">
            {section.title && (
              <SidebarGroupLabel className="px-2 py-1 text-xs font-semibold text-gray-500 uppercase tracking-wider">
                {section.title}
              </SidebarGroupLabel>
            )}
            <SidebarGroupContent>
              <SidebarMenu className="space-y-0">
                {section.items.map((item) => {
                  const isActive = currentPath === item.url;

                  return (
                    <SidebarMenuItem key={item.title}>
                      <SidebarMenuButton
                        asChild
                        isActive={isActive}
                        className={`w-full rounded-md px-2 py-1.5 text-sm font-medium transition-colors duration-150 ${isActive
                            ? "!bg-blue-500 !text-white"
                            : "text-gray-700 hover:bg-gray-100 hover:text-gray-900"
                          }`}
                      >
                        <Link to={item.url} className="flex items-center">
                          <item.icon
                            className={`h-4 w-4 mr-2 ${isActive
                                ? "text-white"
                                : "text-gray-500"
                              }`}
                          />
                          <span>{item.title}</span>
                        </Link>
                      </SidebarMenuButton>
                    </SidebarMenuItem>
                  );
                })}
              </SidebarMenu>
            </SidebarGroupContent>
          </SidebarGroup>
        ))}
      </SidebarContent>
    </Sidebar>
  );

}
