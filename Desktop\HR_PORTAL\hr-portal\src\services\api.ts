// API service for HR Portal
import { type Candidate } from '@/types/candidate';

const API_BASE_URL = 'https://backend.makonissoft.com';

// Types for API requests and responses
export interface ApiRequest {
  user_id: string;
  user_type: string;
  user_name: string;
  page_no: number;
}

export interface ApiUser {
  id: number;
  name: string;
  user_type: string;
  email: string;
  user_name: string;
}

export interface ApiCandidate {
  id: number;
  job_id: number;
  name: string;
  mobile: string;
  email: string;
  client: string;
  current_company: string;
  position: string;
  profile: string;
  current_job_location: string;
  preferred_job_location: string;
  qualifications: string;
  experience: string;
  relevant_experience: string;
  current_ctc: string;
  expected_ctc: string;
  notice_period: string;
  linkedin: string;
  reason_for_job_change: string;
  holding_offer: string;
  recruiter: string;
  management: string | null;
  status: string;
  remarks: string;
  skills: string;
  serving_notice_period: string;
  period_of_notice: string;
  last_working_date: string | null;
  total_offers: number | null;
  highest_package_in_lpa: number | null;
  buyout: boolean;
  date_created: string;
  data_updated_date: string;
  resume_present: boolean;
  comments: Record<string, any>;
  peer_reviewer_level1: string | null;
  peer_reviewer_level2: string | null;
  remind_lwd: boolean;
  user_id: number;
}

export interface ApiResponse {
  candidates: ApiCandidate[];
  name: string;
  user: ApiUser;
}

// API service class
export class ApiService {
  private static async makeRequest<T>(
    endpoint: string,
    data: ApiRequest
  ): Promise<T> {
    try {
      const response = await fetch(`${API_BASE_URL}${endpoint}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      return result;
    } catch (error) {
      console.error('API request failed:', error);
      throw error;
    }
  }

  // Fetch candidates from the API
  static async fetchCandidates(
    userId: string,
    userType: string,
    userName: string,
    pageNo: number = 1
  ): Promise<ApiResponse> {
    const requestData: ApiRequest = {
      user_id: userId,
      user_type: userType,
      user_name: userName,
      page_no: pageNo,
    };

    console.log('Fetching candidates with request:', requestData);
    const response = await this.makeRequest<ApiResponse>('/redisdashboard', requestData);
    console.log('API response received:', response);

    return response;
  }
}

// Helper function to convert API candidate to local Candidate type
export function convertApiCandidateToLocal(apiCandidate: ApiCandidate) {
  return {
    id: apiCandidate.id,
    jobId: apiCandidate.job_id.toString(),
    firstName: apiCandidate.name.split(' ')[0] || '',
    lastName: apiCandidate.name.split(' ').slice(1).join(' ') || '',
    email: apiCandidate.email,
    phone: apiCandidate.mobile,
    client: apiCandidate.client,
    profile: apiCandidate.profile,
    skills: apiCandidate.skills,
    status: mapApiStatusToLocal(apiCandidate.status),
    appliedDate: apiCandidate.date_created,
    source: 'API', // Default value since not provided in API
    experience: parseFloat(apiCandidate.experience) || 0,
    education: apiCandidate.qualifications,
    location: apiCandidate.preferred_job_location,
    salary: `${apiCandidate.current_ctc} - ${apiCandidate.expected_ctc}`,
    notes: apiCandidate.remarks || '',
    lastUpdated: apiCandidate.data_updated_date,
    comment: apiCandidate.reason_for_job_change || '',
    peerReviewer: apiCandidate.peer_reviewer_level1 || apiCandidate.peer_reviewer_level2 || '',
    recruiter: apiCandidate.recruiter,
  };
}

// Map API status to local status
function mapApiStatusToLocal(apiStatus: string): "New" | "Screening" | "Interview" | "Offer" | "Rejected" | "Hired" {
  const statusMap: Record<string, "New" | "Screening" | "Interview" | "Offer" | "Rejected" | "Hired"> = {
    'Internal Screening': 'Screening',
    'New': 'New',
    'Interview': 'Interview',
    'Offer': 'Offer',
    'Rejected': 'Rejected',
    'Hired': 'Hired',
    'Screening': 'Screening',
  };

  return statusMap[apiStatus] || 'New';
}
